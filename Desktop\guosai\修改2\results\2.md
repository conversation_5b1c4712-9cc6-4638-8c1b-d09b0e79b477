# 2 确定 SiC 外延层厚度的算法设计与实现

## 2.1 问题分析

本题已在问题 1 推导出 **双光束干涉** 的厚度公式：

$$
L = 2d\sqrt{n^{2}-\sin^{2}\theta} \quad\Longrightarrow\quad d = \frac{L}{2\sqrt{n^{2}-\sin^{2}\theta}}
$$

其中 $L$ 为光程差、$d$ 为外延层厚度、$n$ 为外延层折射率、$\theta$ 为入射角。

要点：

1. $L$ 难以直接从条纹级次计数获得；
2. $n$ 随波长略变，本题给定 SiC 可近似为常数 2.58；
3. 实测数据为反射率-波数曲线，需要从中 **自动提取 $L$**。

解决思路：

- **FFT 变换**：将反射率的波数域信号转到"光程差域"，主峰即 $L$；
- **线性插值 + 基线校正**：提升 FFT 频率分辨率并消除直流分量；
- **双入射角交叉验证**：10°、15° 两结果一致则方法可靠。

## 2.2 基本假设

H1. 外延层上下表面平行且光滑，只发生一次反射与透射（双光束）。

H2. 在 400–1200 cm$^{-1}$ 区域内，SiC 折射率视为常数 $n=2.58$。

H3. SiC 在红外区吸收较弱，FFT 主峰位置不受吸收效应显著影响。

H4. 光谱噪声服从独立同分布的高斯噪声，可通过基线校正消除。

## 2.3 数学模型建立

### 2.3.1 光程差-厚度公式

沿用问题 1 结果：

$$
d = \frac{L}{2\sqrt{n^{2}-\sin^{2}\theta}}
$$

### 2.3.2 FFT 主峰提取

设插值后反射率序列 $r(k)$，$k$ 为波数 (cm$^{-1}$)。

1. 去直流：$r_c(k)=r(k)-\bar{r}$；
2. 快速傅里叶变换：$R(f)=\mathcal{F}\{r_c(k)\}$；
3. 频率 $f$ (cm) 对应光程差 $L=1/f$；
4. $f_{\max}$ 处主峰给出 $L$。

### 2.3.3 数值优化模型（改进版）

为考虑弱吸收效应，引入经验型消光系数：

$$
\kappa(\lambda)=A\,\lambda^{p}, \qquad A>0,\,p>0
$$

将复折射率 $n_c=n-i\kappa$ 代入 Fabry-Perot 公式，构造理论反射率 $R_{\text{th}}(k;d,A,p)$。

目标函数为：

$$
\min_{d,A,p}\;\sqrt{\frac{1}{N}\sum_{i=1}^{N}\bigl(R_{\text{exp}}(k_i)-R_{\text{th}}(k_i)\bigr)^2}
$$

采用差分进化 (DE) + L-BFGS-B 两阶段优化，变量范围为 $d\in[0.95d_0,1.05d_0]$，$A\in[10^{-5},10^{-3}]$，$p\in[0.5,2.0]$，其中 $d_0$ 为 FFT 初值。

## 2.4 求解流程

对应 `problem2_solution.py` 行号：

1. **数据加载** (`load_data` 293-340)：自动识别编码读取 CSV。
2. **线性插值** (`preprocess_data` 343-370)：步长 ≈0.055 cm$^{-1}$，点数 65 536。
3. **FFT 提取 $L$** (`analyze_fft` 373-416)：10°、15° 主峰同为 0.018331 cm。
4. **传统厚度计算** (`calculate_thickness` 419-451)：$d_{10}=35.605\,\mu\mathrm{m}$，$d_{15}=35.705\,\mu\mathrm{m}$。
5. **数值优化** (`OptimizedThicknessCalculator` 56-216)：得 $d_{10}=36.900\,\mu\mathrm{m}$，$d_{15}=36.901\,\mu\mathrm{m}$。
6. **可靠性分析** (`analyze_reliability`)：传统误差 0.28%，优化误差 0.001%。
7. **可视化**：生成步骤图、详细分析图、结果对比图等 5 张图片。

## 2.5 结果展示与分析

### 2.5.1 传统 FFT 结果

| 入射角         | 光程差$L$ (cm) | 厚度$d$ (μm)                 |
| -------------- | ---------------- | ------------------------------- |
| 10°           | 0.018331         | 35.605                          |
| 15°           | 0.018331         | 35.705                          |
| **平均** | —               | **35.655 ±0.05** (0.28%) |

> 见 `problem2_calculation_steps_*` 图片；结果可靠性评估为"优秀"。

### 2.5.2 数值优化结果

| 入射角         | $A$ (×10⁻³) | $p$ | 厚度$d$ (μm)                   |
| -------------- | ---------------- | ----- | --------------------------------- |
| 10°           | 1.000            | 1.927 | **36.900**                  |
| 15°           | 1.000            | 1.945 | **36.901**                  |
| **平均** | —               | —    | **36.901 ±0.001** (0.001%) |

- 优化厚度比传统大 ≈1.25 μm；考虑吸收后有效折射率下降，需要更厚的薄膜厚度以满足相同的 $L$。

### 2.5.3 可靠性与精度

- **传统法**：双角度误差 0.28%，满足工程精度要求；
- **优化法**：误差 0.001%，对高精度器件仿真更有利。

## 2.6 结论

1. **算法**：FFT + 常数折射率即可达到 0.3% 精度，引入吸收修正可达到 0.001%。
2. **厚度**：$d=35.655\,\mu\mathrm{m}$（传统）或 $d=36.901\,\mu\mathrm{m}$（优化）。
3. **可靠性**：双角度误差远低于 5% 工程阈值。
4. **意义**：方法无需条纹计数，快速稳健；若需极致精度，可启用消光系数优化。

---

### 符号说明

| 符号        | 含义         | 单位        | 备注                   |
| ----------- | ------------ | ----------- | ---------------------- |
| $k$       | 波数         | cm$^{-1}$ | 实验横轴               |
| $\lambda$ | 波长         | μm         | $\lambda=10^4/k$     |
| $r(k)$    | 反射率       | %           | 原始光谱               |
| $L$       | 光程差       | cm          | FFT 主峰$1/f_{\max}$ |
| $d$       | 外延层厚度   | μm         | 待求量                 |
| $n$       | 外延层折射率 | —          | 近似 2.58              |
| $\theta$  | 入射角       | °          | 10°, 15°             |
| $\kappa$  | 消光系数     | —          | $\kappa=A\lambda^p$  |
| $A,p$     | 消光模型参数 | —          | 优化变量               |
