#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于问题3结果生成消光系数分析数据表
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy.constants import c, e, epsilon_0, m_e
import os

plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def extinction_coefficient_model(wavelength, A, p):
    """消光系数模型：κ = A * λ^p"""
    return A * (wavelength ** p)

def calculate_extinction_from_carriers(wavelength_um, carrier_concentration, material='Si'):
    """基于载流子浓度计算消光系数"""
    if material.upper() == 'SI':
        m_eff = 0.26 * m_e
        gamma = 5e12
        n0 = 3.55
    elif material.upper() == 'SIC':
        m_eff = 0.67 * m_e
        gamma = 1e13
        n0 = 2.6
    else:
        raise ValueError(f"不支持的材料: {material}")
    
    if carrier_concentration <= 0:
        return np.zeros_like(wavelength_um)
    
    N_m3 = carrier_concentration * 1e6
    omega_p_sq = N_m3 * e**2 / (epsilon_0 * m_eff)
    omega_p = np.sqrt(omega_p_sq)
    
    omega = 2 * np.pi * c / (wavelength_um * 1e-6)
    epsilon_imag = omega_p**2 * gamma / (omega * (omega**2 + gamma**2))
    kappa = epsilon_imag / (2 * n0)
    
    return kappa

def fit_extinction_parameters(wavelength_um, kappa_values):
    """拟合消光系数参数A和p"""
    valid_mask = (kappa_values > 0) & (wavelength_um > 0)
    if np.sum(valid_mask) < 3:
        return 0.0, 0.0, np.inf, 0.0
    
    wl_valid = wavelength_um[valid_mask]
    kappa_valid = kappa_values[valid_mask]
    
    try:
        log_wl = np.log(wl_valid)
        log_kappa = np.log(kappa_valid)
        
        coeffs = np.polyfit(log_wl, log_kappa, 1)
        p = coeffs[0]
        log_A = coeffs[1]
        A = np.exp(log_A)
        
        kappa_fitted = extinction_coefficient_model(wl_valid, A, p)
        rmse = np.sqrt(np.mean((kappa_valid - kappa_fitted)**2))
        
        ss_res = np.sum((kappa_valid - kappa_fitted)**2)
        ss_tot = np.sum((kappa_valid - np.mean(kappa_valid))**2)
        r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
        
        return A, p, rmse, r_squared
        
    except Exception as e:
        print(f"拟合失败: {e}")
        return 0.0, 0.0, np.inf, 0.0

def generate_extinction_analysis_data():
    """基于问题3结果生成消光系数分析数据"""
    print("="*60)
    print("基于问题3结果生成消光系数分析数据表")
    print("="*60)
    
    # 从问题3结果中提取的数据
    problem3_results = {
        'Si_10deg': {
            'thickness_um': 25.423,
            'carrier_concentration': 5.0e15,
            'refractive_index': 3.5548,
            'angle': 10.0,
            'material': 'Si'
        },
        'Si_15deg': {
            'thickness_um': 25.462,
            'carrier_concentration': 1.0e16,
            'refractive_index': 3.5545,
            'angle': 15.0,
            'material': 'Si'
        },
        'SiC_10deg': {
            'thickness_um': 35.655,
            'carrier_concentration': 1.0e16,
            'refractive_index': 2.60,
            'angle': 10.0,
            'material': 'SiC'
        },
        'SiC_15deg': {
            'thickness_um': 35.655,
            'carrier_concentration': 1.0e16,
            'refractive_index': 2.60,
            'angle': 15.0,
            'material': 'SiC'
        }
    }
    
    wavelength_range = np.linspace(8.0, 25.0, 100)
    analysis_results = []
    
    print("\n计算各样品的消光系数参数...")
    print("-" * 50)
    
    for sample_id, data in problem3_results.items():
        print(f"\n🔍 分析样品: {sample_id}")
        print(f"  材料: {data['material']}")
        print(f"  厚度: {data['thickness_um']:.3f} μm")
        print(f"  载流子浓度: {data['carrier_concentration']:.2e} cm⁻³")
        
        kappa_values = calculate_extinction_from_carriers(
            wavelength_range, 
            data['carrier_concentration'], 
            data['material']
        )
        
        A, p, rmse, r_squared = fit_extinction_parameters(wavelength_range, kappa_values)
        
        print(f"  消光系数参数A: {A:.6e}")
        print(f"  消光系数参数p: {p:.4f}")
        print(f"  拟合RMSE: {rmse:.6e}")
        
        result = {
            '样品': sample_id,
            '材料': data['material'],
            '厚度 d (μm)': data['thickness_um'],
            '载流子浓度 N (cm⁻³)': data['carrier_concentration'],
            '消光系数参数 A': A,
            '消光系数参数 p': p,
            '拟合误差 (RMSE)': rmse,
            '入射角 (°)': data['angle'],
            '折射率 n': data['refractive_index']
        }
        
        analysis_results.append(result)
    
    df = pd.DataFrame(analysis_results)
    
    print(f"\n📊 消光系数分析数据表")
    print("="*100)
    print(df.to_string(index=False, float_format='%.6g'))
    
    # 保存结果
    output_dir = 'results'
    os.makedirs(output_dir, exist_ok=True)
    
    csv_file = f'{output_dir}/extinction_coefficient_analysis.csv'
    df.to_csv(csv_file, index=False, encoding='utf-8-sig')
    print(f"\n✓ 数据已保存为CSV: {csv_file}")
    
    excel_file = f'{output_dir}/extinction_coefficient_analysis.xlsx'
    df.to_excel(excel_file, index=False, engine='openpyxl')
    print(f"✓ 数据已保存为Excel: {excel_file}")
    
    return df

if __name__ == "__main__":
    df_result = generate_extinction_analysis_data()