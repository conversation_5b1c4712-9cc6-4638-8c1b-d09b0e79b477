# -*- coding: utf-8 -*-
"""
碳化硅外延层厚度测定核心算法库
提取自问题1、2、3的核心算法实现

本文件包含以下核心算法：
1. 折射率模型（<PERSON>llmeier方程 + Drude模型）
2. FFT光程差分析算法
3. 厚度计算算法（传统方法和优化方法）
4. 载流子浓度估计算法
5. 数据预处理算法
6. 可靠性分析算法
7. 多材料支持算法
"""

import numpy as np
import pandas as pd
from scipy.interpolate import interp1d
from scipy.fft import fft, fftfreq
from scipy.optimize import minimize_scalar, minimize, differential_evolution
from scipy.constants import c, e, epsilon_0, m_e
import warnings
warnings.filterwarnings('ignore')


# ==================== 1. 折射率模型算法 ====================

class RefractiveIndexModel:
    """
    波长依赖折射率模型类
    实现Sellmeier方程和Drude模型的组合：n(λ, N) = √[n₀²(λ) - ωₚ²(N)/(ω² + γ²)]
    """
    
    def __init__(self, material='SiC'):
        self.material = material.upper()
        
        if self.material == 'SIC':
            # SiC的Sellmeier方程参数
            self.A = 6.7
            self.B1 = 1.73
            self.C1 = 0.256
            self.B2 = 0.32
            self.C2 = 1250.0
            self.m_eff = 0.67 * m_e  # 电子有效质量
            self.gamma = 1e13        # 阻尼常数
        elif self.material == 'SI':
            # Si的Sellmeier方程参数
            self.A = 11.7
            self.B1 = 0.939
            self.C1 = 0.0513
            self.B2 = 8.1e-3
            self.C2 = 1.16e6
            self.m_eff = 0.26 * m_e
            self.gamma = 5e12
    
    def intrinsic_refractive_index(self, wavelength_um):
        """计算本征折射率（Sellmeier方程）"""
        lambda_sq = wavelength_um**2
        n_sq = self.A + (self.B1 * lambda_sq) / (lambda_sq - self.C1) + \
               (self.B2 * lambda_sq) / (lambda_sq - self.C2)
        return np.sqrt(np.maximum(n_sq, 1.0))
    
    def plasma_frequency(self, carrier_concentration):
        """计算等离子体频率：ωₚ = √(Ne²/(ε₀m*))"""
        if carrier_concentration <= 0:
            return 0
        N_m3 = carrier_concentration * 1e6  # cm⁻³ to m⁻³
        omega_p_sq = N_m3 * e**2 / (epsilon_0 * self.m_eff)
        return np.sqrt(omega_p_sq)
    
    def refractive_index_with_carriers(self, wavelength_um, carrier_concentration):
        """计算考虑载流子效应的折射率（Drude模型）"""
        n0 = self.intrinsic_refractive_index(wavelength_um)
        
        if carrier_concentration <= 0:
            return n0
        
        omega = 2 * np.pi * c / (wavelength_um * 1e-6)
        omega_p = self.plasma_frequency(carrier_concentration)
        
        epsilon_inf = n0**2
        epsilon_carrier = -omega_p**2 / (omega**2 + 1j*self.gamma*omega)
        epsilon_total = epsilon_inf + epsilon_carrier
        
        n_with_carriers = np.sqrt(np.real(epsilon_total))
        return np.maximum(n_with_carriers, 1.0)


# ==================== 2. 载流子浓度估计算法 ====================

class CarrierConcentrationEstimator:
    """载流子浓度估计器 - 通过光谱形状自动估计掺杂载流子浓度"""
    
    def __init__(self, material='SiC'):
        self.material = material.upper()
        self.ri_model = RefractiveIndexModel(material)
    
    def estimate_from_spectrum_shape(self, wavenumber, reflectance):
        """从光谱形状估计载流子浓度"""
        long_wave_mask = wavenumber >= 800  # 分析长波段
        if np.sum(long_wave_mask) < 5:
            return self._get_default_concentration()
        
        wn_long = wavenumber[long_wave_mask]
        ref_long = reflectance[long_wave_mask]
        
        if len(wn_long) > 1:
            slope = np.polyfit(wn_long, ref_long, 1)[0]
            
            if self.material == 'SIC':
                if slope < -0.008:
                    return 5e16  # 高掺杂
                elif slope < -0.004:
                    return 2e16  # 中等掺杂
                else:
                    return 8e15  # 低掺杂
            elif self.material == 'SI':
                if slope < -0.005:
                    return 1e16
                elif slope < -0.002:
                    return 5e15
                else:
                    return 1e15
        
        return self._get_default_concentration()
    
    def _get_default_concentration(self):
        """获取材料的默认载流子浓度"""
        return 1e16 if self.material == 'SIC' else 1e15


# ==================== 3. 数据预处理算法 ====================

def load_spectral_data(file_path):
    """加载光谱数据文件，支持多种编码格式"""
    encodings = ['gbk', 'gb2312', 'utf-8', 'latin-1']
    
    for encoding in encodings:
        try:
            if file_path.endswith('.xlsx'):
                df = pd.read_excel(file_path)
            else:
                df = pd.read_csv(file_path, encoding=encoding)
            
            wavenumber = df.iloc[:, 0].to_numpy()
            reflectance = df.iloc[:, 1].to_numpy()
            
            # 移除无效数据
            valid_mask = ~(np.isnan(wavenumber) | np.isnan(reflectance))
            return wavenumber[valid_mask], reflectance[valid_mask]
            
        except:
            continue
    
    raise ValueError(f"无法读取文件 {file_path}")

def preprocess_and_interpolate(wavenumber, reflectance, num_points=2**16):
    """数据预处理：线性插值生成均匀间隔的数据点"""
    interp_func = interp1d(wavenumber, reflectance, kind='linear', 
                          bounds_error=False, fill_value='extrapolate')
    uniform_wavenumber = np.linspace(wavenumber.min(), wavenumber.max(), num_points)
    uniform_reflectance = interp_func(uniform_wavenumber)
    return uniform_wavenumber, uniform_reflectance


# ==================== 4. FFT光程差分析算法 ====================

def calculate_opd_from_fft(uniform_wavenumber, uniform_reflectance):
    """通过FFT计算光程差 (Optical Path Difference, OPD)"""
    N = len(uniform_wavenumber)
    wavenumber_step = uniform_wavenumber[1] - uniform_wavenumber[0]
    
    # 去除直流分量
    reflectance_centered = uniform_reflectance - np.mean(uniform_reflectance)
    reflectance_fft = fft(reflectance_centered)
    fft_magnitude = np.abs(reflectance_fft)
    
    # 计算FFT频率轴（对应光程差）
    opd_axis = fftfreq(N, d=wavenumber_step)
    positive_opd_axis = opd_axis[:N // 2]
    positive_fft_magnitude = fft_magnitude[:N // 2]
    
    # 寻找主峰位置
    start_idx = max(1, int(0.001 * N))
    peak_index = np.argmax(positive_fft_magnitude[start_idx:]) + start_idx
    opd_value = positive_opd_axis[peak_index]
    
    return opd_value, positive_opd_axis, positive_fft_magnitude


# ==================== 5. 厚度计算算法 ====================

def calculate_thickness_traditional(opd, n1, theta_i_deg):
    """传统方法：根据光程差、固定折射率和入射角计算外延层厚度"""
    theta_i_rad = np.deg2rad(theta_i_deg)
    denominator = 2 * np.sqrt(n1**2 - np.sin(theta_i_rad)**2)
    thickness_cm = opd / denominator
    return thickness_cm * 1e4  # 转换为微米

def calculate_thickness_optimized(wavenumber, reflectance, opd, theta_i_deg, material='SiC'):
    """优化方法：基于波长依赖折射率模型和载流子效应的厚度计算"""
    ri_model = RefractiveIndexModel(material)
    carrier_estimator = CarrierConcentrationEstimator(material)
    
    # 估计载流子浓度
    estimated_N = carrier_estimator.estimate_from_spectrum_shape(wavenumber, reflectance)
    
    # 计算波长依赖的折射率
    analysis_mask = (wavenumber >= 500) & (wavenumber <= 1500)
    if np.sum(analysis_mask) > 0:
        wn_analysis = wavenumber[analysis_mask]
        wavelength_um = 1e4 / wn_analysis
        ref_analysis = reflectance[analysis_mask]
    else:
        wavelength_um = 1e4 / wavenumber
        ref_analysis = reflectance
    
    n_wavelength = ri_model.refractive_index_with_carriers(wavelength_um, estimated_N)
    
    # 计算加权平均折射率
    if np.sum(analysis_mask) > 0:
        weights = np.abs(ref_analysis - np.mean(ref_analysis))
        weights = weights / np.sum(weights) if np.sum(weights) > 0 else np.ones_like(weights) / len(weights)
        n_avg_optimized = np.average(n_wavelength, weights=weights)
    else:
        n_avg_optimized = np.mean(n_wavelength)
    
    # 计算厚度
    theta_i_rad = np.deg2rad(theta_i_deg)
    denominator = 2 * np.sqrt(n_avg_optimized**2 - np.sin(theta_i_rad)**2)
    thickness_cm = opd / denominator
    thickness_um = thickness_cm * 1e4
    
    return {
        'thickness_um': thickness_um,
        'carrier_concentration': estimated_N,
        'optimized_n_avg': n_avg_optimized,
        'wavelength_range': (wavelength_um.min(), wavelength_um.max()),
        'material': material
    }


# ==================== 6. 可靠性分析算法 ====================

def analyze_reliability(thickness_10, thickness_15):
    """分析两个入射角计算结果的可靠性"""
    avg_thickness = (thickness_10 + thickness_15) / 2
    absolute_error = abs(thickness_10 - thickness_15)
    relative_error = (absolute_error / avg_thickness) * 100
    
    if relative_error < 1.0:
        reliability = "优秀"
        level = "excellent"
    elif relative_error < 5.0:
        reliability = "良好"
        level = "good"
    elif relative_error < 10.0:
        reliability = "可接受"
        level = "acceptable"
    else:
        reliability = "需改进"
        level = "poor"
    
    return {
        'thickness_10': thickness_10,
        'thickness_15': thickness_15,
        'average': avg_thickness,
        'absolute_error': absolute_error,
        'relative_error': relative_error,
        'reliability': reliability,
        'level': level
    }


# ==================== 7. 多光束干涉分析算法 ====================

def analyze_multibeam_interference_condition(n1, n2, thickness_um, wavelength_um):
    """分析多光束干涉的必要条件"""
    # 计算反射系数
    r1 = (1 - n1) / (1 + n1)  # 空气-外延层界面
    r2 = (n1 - n2) / (n1 + n2)  # 外延层-衬底界面
    
    # 计算精细度
    finesse = 4 * r1 * r2 / (1 - r1 * r2)**2
    
    # 多光束干涉条件：精细度 > 阈值
    multibeam_threshold = 0.1  # 经验阈值
    is_multibeam = finesse > multibeam_threshold
    
    return {
        'finesse': finesse,
        'r1': r1,
        'r2': r2,
        'is_multibeam': is_multibeam,
        'threshold': multibeam_threshold
    }


# ==================== 8. 消光系数模型算法 ====================

class ExtinctionCoefficientModel:
    """消光系数模型：κ = A × λᵖ"""
    
    def __init__(self):
        self.n_base = 2.6  # 基础折射率
    
    def calculate_extinction(self, wavelength_um, A, p):
        """计算消光系数"""
        return A * (wavelength_um ** p)
    
    def get_base_refractive_index(self):
        """获取基础折射率"""
        return self.n_base


# ==================== 9. 数值优化算法 ====================

def numerical_optimization_thickness(wavenumber, reflectance, angle_deg, bounds):
    """数值优化求解厚度参数"""
    
    def objective_function(params):
        """目标函数：最小化理论与实测反射率的差异"""
        d_um, A, p = params
        
        # 计算理论反射率（简化模型）
        wavelength_um = 1e4 / wavenumber
        extinction = A * (wavelength_um ** p)
        
        # 简化的Fabry-Perot干涉模型
        n_base = 2.6
        angle_rad = np.deg2rad(angle_deg)
        d_cm = d_um * 1e-4
        
        optical_path = 2 * n_base * d_cm * np.sqrt(1 - (np.sin(angle_rad) / n_base)**2)
        phase = 2 * np.pi * optical_path / (wavelength_um * 1e-4)
        
        # 考虑消光的衰减
        attenuation = np.exp(-2 * extinction * optical_path / (wavelength_um * 1e-4))
        theoretical_reflectance = 50 * (1 + attenuation * np.cos(phase))  # 简化模型
        
        # 计算拟合误差
        error = np.sqrt(np.mean((reflectance - theoretical_reflectance)**2))
        return error
    
    # 差分进化全局优化
    result = differential_evolution(
        objective_function, 
        bounds, 
        seed=42,
        maxiter=100,
        popsize=10
    )
    
    return result
