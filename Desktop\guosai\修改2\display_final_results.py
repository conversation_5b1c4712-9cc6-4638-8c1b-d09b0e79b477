#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示问题3的最终消光系数分析结果表格
"""

import pandas as pd
import numpy as np

def display_extinction_results():
    """显示格式化的消光系数分析结果"""
    
    print("="*80)
    print("问题3：消光系数分析结果数据表")
    print("="*80)
    
    # 基于问题3结果的数据
    data = {
        '样品类型': ['硅(Si) 10°', '硅(Si) 15°', '碳化硅(SiC) 10°', '碳化硅(SiC) 15°'],
        '厚度 d (μm)': [25.423, 25.462, 35.655, 35.655],
        '载流子浓度 N (cm⁻³)': [5.0e15, 1.0e16, 1.0e16, 1.0e16],
        '消光系数参数 A': [6.497e-09, 1.299e-08, 1.408e-08, 1.408e-08],
        '消光系数参数 p': [2.997, 2.997, 2.986, 2.986],
        '拟合误差 (RMSE)': [1.818e-08, 3.635e-08, 1.504e-07, 1.504e-07]
    }
    
    df = pd.DataFrame(data)
    
    print("基于载流子浓度的消光系数模型：κ = A × λᵖ")
    print("其中 λ 为波长(μm)，κ 为消光系数")
    print("\n")
    
    # 自定义格式化输出
    print(f"{'样品类型':<15} {'厚度 d (μm)':<12} {'载流子浓度 N (cm⁻³)':<18} {'消光系数参数 A':<16} {'消光系数参数 p':<14} {'拟合误差 (RMSE)':<16}")
    print("-" * 110)
    
    for i, row in df.iterrows():
        print(f"{row['样品类型']:<15} {row['厚度 d (μm)']:<12.3f} {row['载流子浓度 N (cm⁻³)']:<18.2e} {row['消光系数参数 A']:<16.3e} {row['消光系数参数 p']:<14.3f} {row['拟合误差 (RMSE)']:<16.3e}")
    
    print("\n" + "="*80)
    print("主要发现和结论：")
    print("="*80)
    
    print("\n1. 厚度计算结果：")
    print(f"   • 硅(Si)外延层平均厚度：{np.mean([25.423, 25.462]):.3f} μm")
    print(f"   • 碳化硅(SiC)外延层厚度：35.655 μm (来自问题2)")
    
    print("\n2. 载流子浓度：")
    print(f"   • 硅(Si)样品：5.0×10¹⁵ ~ 1.0×10¹⁶ cm⁻³")
    print(f"   • 碳化硅(SiC)样品：1.0×10¹⁶ cm⁻³")
    
    print("\n3. 消光系数参数：")
    print(f"   • 参数A范围：6.5×10⁻⁹ ~ 1.4×10⁻⁸")
    print(f"   • 参数p值：约2.99 (Si) 和 2.99 (SiC)")
    print(f"   • 符合λ³幂次关系（p ≈ 3）")
    
    print("\n4. 拟合质量：")
    print(f"   • RMSE值均在10⁻⁸数量级")
    print(f"   • 载流子模型能很好地解释消光系数的波长依赖性")
    
    print("\n5. 材料对比：")
    print(f"   • SiC具有更高的消光系数参数A")
    print(f"   • 两种材料的p值相近，都接近3")
    print(f"   • 验证了自由载流子吸收的λ³依赖关系")
    
    return df

if __name__ == "__main__":
    df = display_extinction_results()